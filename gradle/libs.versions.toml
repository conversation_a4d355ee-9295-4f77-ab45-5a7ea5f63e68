[plugins]
# Ko<PERSON><PERSON>
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
# Utilities
versions = "com.github.ben-manes.versions:0.52.0"
test-logger = "com.adarshr.test-logger:4.0.0"
# Code generators
codegen-tranop = "no.ruter.tranop.tranop-codegen-gradle-plugin:3.1.57"
codegen-openapi = "org.openapi.generator:7.14.0"
spring-boot = { id = "org.springframework.boot", version.ref = "spring-boot" }
spring-dependencies = { id = "io.spring.dependency-management", version.ref = "spring-dependency-management" }
avro = { id = "com.github.davidmc24.gradle.plugin.avro", version.ref = "gradle-avro" }

[versions]
java = "21"
kotlin = "2.2.0"
confluent = "8.0.0"
rdp-insight = "1.0.15"
logstash-logback-encoder = "8.1"
adt-doc-v1 = "1.8.0"
adt-doc-v2 = "2.11.0"
adt-doc-v3 = "3.5.0"
adt-doc-v4 = "7250ab4"
assignment-adt-v4-api-model = "v3.22.313"
plandata-journey-api-internal = "v8.0.346"
entity-schemas = "7a5ec609817e9dabea3704bc7394bf20ec8892eb"
spring-boot = "3.5.3"
spring-dependency-management = "1.1.7"
gradle-avro = "1.9.1"
avro = "1.12.0"

[bundles]
junit = [
    "spring-boot-starter-test",
]
kotlin = [
    "kotlin-reflect",
    "kotlin-stdlib",
]
jackson = [
    "jackson-annotations",
    "jackson-core",
    "jackson-databind",
    "jackson-dataformat-yaml",
    "jackson-kotlin",
]
spring-boot = [
    "spring-boot-starter-web",
]
insight = [
    "logstash-logback-encoder",
    "micrometer-registry-prometheus",
    "rdp-insight",
    "spring-boot-starter-actuator",
    "spring-boot-starter-logging",
]
avro = [
    "avro",
    "confluent-avro-serde",
]

[libraries]
avro = { module = "org.apache.avro:avro", version.ref = "avro" }
confluent-avro-serde = { module = "io.confluent:kafka-streams-avro-serde", version.ref = "confluent" }
kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version.ref = "kotlin" }
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }
spring-kafka = {module = "org.springframework.kafka:spring-kafka"}
kafka-streams-test-utils = { module = "org.apache.kafka:kafka-streams-test-utils" }
commons-lang3 = "org.apache.commons:commons-lang3:3.17.0"
rdp-json-utils = "no.ruter.rdp.common:rdp-json-utils:2.0.2"
jackson-core = { module = "com.fasterxml.jackson.core:jackson-core" }
jackson-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin"}
jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind" }
jackson-annotations = { module = "com.fasterxml.jackson.core:jackson-annotations" }
jackson-dataformat-yaml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml" }
spring-boot-starter-logging = { module = "org.springframework.boot:spring-boot-starter-logging" }
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator" }
spring-boot-starter-web = { module = "org.springframework.boot:spring-boot-starter-web" }
spring-boot-starter-test = { module = "org.springframework.boot:spring-boot-starter-test" }
micrometer-registry-prometheus = { module = "io.micrometer:micrometer-registry-prometheus" }
rdp-insight = { module = "no.ruter.rdp.insight:rdp-insight", version.ref = "rdp-insight" }
logstash-logback-encoder = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logstash-logback-encoder" }
