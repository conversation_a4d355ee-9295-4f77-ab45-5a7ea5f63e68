import com.github.benmanes.gradle.versions.updates.DependencyUpdatesTask
import no.ruter.tranop.codegen.json.schema.JsonSchemaFormat
import org.ajoberstar.grgit.Credentials
import org.ajoberstar.grgit.Grgit
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    // Base plugins
    id("java")
    id("jacoco")
    id("java-library")
    id("maven-publish")
    alias(libs.plugins.kotlin.jvm)
    id("com.dorongold.task-tree") version "4.0.1"

    alias(libs.plugins.spring.boot)
    alias(libs.plugins.spring.dependencies)

    // Code generation / validation plugins.
    alias(libs.plugins.codegen.tranop)
    alias(libs.plugins.codegen.openapi)
    id("org.ajoberstar.grgit") version "5.3.2"

    // Utility plugins.
    alias(libs.plugins.versions) // Dependency version management.
    alias(libs.plugins.test.logger) // Improved test logging.

    alias(libs.plugins.avro)
}

// We only set <major>.<minor> in version, since .<revision> is set automatically by build pipeline.
group = "no.ruter.tranop"
version = "38.0-SNAPSHOT"

// Always show stack trace, in case code generation fails.
gradle.startParameter.showStacktrace = ShowStacktrace.ALWAYS

val javaVersion = JavaLanguageVersion.of(libs.versions.java.get())
java {
    withSourcesJar()
    toolchain {
        languageVersion = javaVersion
    }
}
jacoco {
    toolVersion = "0.8.11"
}
tasks.jacocoTestReport {
    reports {
        csv.required.set(true)
        xml.required.set(true)
        html.required.set(true)
    }
}

tasks.bootJar {
    enabled = false
}

tasks.jar {
    enabled = true
}

val gitLabPrivateToken: String by project.extra
val jobToken = System.getenv("CI_JOB_TOKEN")?.trim() ?: gitLabPrivateToken.trim()
val gitLabCI = System.getenv().getOrDefault("CI", "false").toBoolean()
val gitLabProjectId: String = System.getenv().getOrDefault("CI_PROJECT_ID", "<missing-ci-project-id>")

var gitLabUsername = if (gitLabCI) "gitlab-ci-token" else "gitlab-private-token"
var gitLabPassword = if (gitLabCI) jobToken else gitLabPrivateToken

repositories {
    mavenLocal()
    gitlabGroup(groupId = "6569239", groupName = "ruter-as")
    gitlabGroup(groupId = "57160432", groupName = "ruter-as/tranop")
    gitlabGroup(groupId = "95793656", groupName = "ruter-as/rdp/insight")
    gitlabGroup(groupId = "9909697", groupName = "ruter-as/rdp")
    mavenCentral()
    maven("https://packages.confluent.io/maven/") {
        content { includeGroup("io.confluent") }
    }
}

publishing {
    publications {
        create<MavenPublication>(name = "library") {
            from(components["java"])
        }
    }
    repositories {
        if (gitLabCI) {
            maven {
                // Publish to GitLab project registry.
                url = uri(path = "https://gitlab.com/api/v4/projects/$gitLabProjectId/packages/maven")
                name = "gitlabProjectMaven"
                credentials(HttpHeaderCredentials::class.java) {
                    name = "Job-Token"
                    value = jobToken
                }
                authentication {
                    create<HttpHeaderAuthentication>(name = "header")
                }
            }
        }
    }
}

dependencies {
    implementation(libs.rdp.json.utils)
    compileOnly(libs.rdp.insight)

    implementation(libs.logstash.logback.encoder)
    implementation(libs.bundles.insight)
    implementation(libs.bundles.spring.boot)

    implementation(libs.bundles.avro)

    // Compile dependencies
    compileOnly(libs.spring.kafka)
    compileOnly(libs.bundles.jackson)

    // JUnit
    testImplementation(libs.bundles.junit)

    // Test dependencies
    testImplementation(libs.rdp.insight)
    testImplementation(libs.commons.lang3)
    testImplementation(libs.bundles.kotlin)
    testImplementation(libs.kafka.streams.test.utils)
}

// https://github.com/davidmc24/gradle-avro-plugin
avro {
    fieldVisibility.set("PRIVATE")
}

val buildDir = layout.buildDirectory.get()
val workdir = "$buildDir/tmp"
val clonedSchemaDir = "$workdir/schema-repos"
val localSchemaDir = file("src/main/resources/no/ruter/tranop")
val codegenTargetDir = "${layout.buildDirectory.get()}/codegen"
val importedSchemasDir = file("src/main/resources/imported-schemas")

data class GitSpec(
    val url: String,
    val tag: Provider<String>,
    val credentials: Any? = null,
)

data class SchemaSpec(
    val generators: List<ApiGenerator> = listOf(ApiGenerator.TRANOP_JSON),
    val name: String,
    val version: String,
    val git: GitSpec? = null,
    val schemaFolder: File = File(clonedSchemaDir, "$name$version"),
    val schemaPath: String = "",
    val namePrefix: String = name.uppercase(),
    val filePatterns: List<String> = listOf("**/*.json"),
    val packagePrefix: String = "no.ruter.tranop.$name.dto",
    val kafkaSerdeSchemas: List<String> = emptyList(),
    val apiSpecFilePath: String? = null,
    val valueTypes: Boolean = true,
    val workDir: File =
        file("$importedSchemasDir/${packagePrefix.replace(".", "/")}"),
    val replacements: Map<String, String> = emptyMap(),
)

enum class ApiGenerator(
    val generatorName: String,
) {
    SPRING(generatorName = "kotlin-spring"),
    TRANOP_JSON(generatorName = "tranopCodegen"),
    AVRO(generatorName = "avro"),
    ;

    companion object {
        val openApiGenerators = listOf(SPRING)
        val tranopGenerators = listOf(TRANOP_JSON)
    }

    fun isOpenApi() = openApiGenerators.contains(this)

    fun isTranop() = tranopGenerators.contains(this)
}

val avroSpec =
    SchemaSpec(
        generators = listOf(ApiGenerator.AVRO),
        name = "entities",
        version = "1",
        workDir = file("src/main/avro"),
        git =
            GitSpec(
                url = "https://gitlab.com/ruter-as/arkitektur/skjemakatalog/schemas.git",
                tag = libs.versions.entity.schemas,
                credentials = Credentials(gitLabUsername, gitLabPassword),
            ),
        schemaPath = "Schemas",
        filePatterns =
            listOf(
                "Common/**/*.avsc",
                "DatedJourney/**/*.avsc",
                "Vehicle/**/*api.v1.avsc",
                "Assignment/**/*.avsc",
                "Operational/**/*.avsc",
                "Traffic/entity.traffic.event.key.v1.avsc",
            ),
    )

val yamlPattern = listOf("**/*.yml")
val pdDatedJourneyBasePackage = "no.ruter.plandata.journey.dated.v2.dto"

val specs =
    listOf(
        SchemaSpec(
            name = "common",
            namePrefix = "DTO",
            packagePrefix = "no.ruter.tranop.common.dto",
            version = "1",
            schemaFolder = File(localSchemaDir, "common/dto"),
            filePatterns = yamlPattern,
        ),
        SchemaSpec(
            name = "assignment",
            namePrefix = "DTO",
            version = "1",
            schemaFolder = File(localSchemaDir, "assignment/dto"),
            kafkaSerdeSchemas =
                listOf(
                    "assignment.json",
                ),
        ),
        SchemaSpec(
            name = "datedJourney",
            namePrefix = "DTO",
            packagePrefix = "no.ruter.tranop.dated.journey.dto", // TODO: Change this to .journey.dated.dto., eventually...
            version = "2",
            schemaFolder = File(localSchemaDir, "journey/dated/dto"),
            kafkaSerdeSchemas =
                listOf(
                    "common/link/stop-point-link.json",
                    "common/stop/stop-point.json",
                    "dated-journey.json",
                ),
        ),
        SchemaSpec(
            name = "outbox",
            namePrefix = "DB",
            version = "1",
            valueTypes = true,
            schemaFolder = File(localSchemaDir, "outbox/db"),
            packagePrefix = "no.ruter.tranop.outbox",
            filePatterns = yamlPattern,
        ),
        SchemaSpec(
            name = "plandata",
            version = "1",
            namePrefix = "PDJ", // Plandata (DTO) DatedJourney
            git =
                GitSpec(
                    url = "https://gitlab.com/ruter-as/plandata/libraries/journey-api-internal.git",
                    tag = libs.versions.plandata.journey.api.internal,
                    credentials = Credentials(gitLabUsername, gitLabPassword),
                ),
            // Need this prefix for the existing java types to work.
            // Note the resulting package is somewhat different from the one we produce locally
            packagePrefix = pdDatedJourneyBasePackage,
            schemaPath = "src/main/resources/no/ruter/tranop/journey/dto/v2",
            valueTypes = true,
            filePatterns =
                listOf(
                    "**/common/**/*.json",
                    "**/dated/*.json",
                    "**/journey-call.json", // To get a hold of StopPointBehaviourType
                    "**/journey.json", // To get journey type
                    "**/journey-references.json", // Inline from journey.json
                    "**/journey-plan.json", // Inline from journey.json
                    "**/destination-display.json",
                ),
            replacements =
                mapOf(
                    "no.ruter.tranop.journey.dto.v2.model.common.DTOMessageHeader" to "$pdDatedJourneyBasePackage.model.common.PDJMessageHeader",
                    "no.ruter.tranop.journey.dto.v2.model.dated.DTO" to "$pdDatedJourneyBasePackage.value.PDJ",
                    "no.ruter.tranop.journey.dto.v2.model.common.DTO" to "$pdDatedJourneyBasePackage.value.PDJ",
                    "no.ruter.tranop.journey.dto.v2.model.common.link.DTO" to "$pdDatedJourneyBasePackage.value.PDJ",
                ),
            kafkaSerdeSchemas =
                listOf(
                    "common/link/stop-point-link.json",
                    "common/stop/stop-point.json",
                    "dated/dated-journey.json",
                    "destination-display.json",
                ),
        ),
        SchemaSpec(
            name = "operationCommonDto",
            namePrefix = "DTO",
            packagePrefix = "no.ruter.tranop.operation.common.dto",
            version = "1",
            schemaFolder = File(localSchemaDir, "operation/common/dto"),
            filePatterns = yamlPattern,
        ),
        SchemaSpec(
            name = "operationCommonBi",
            namePrefix = "BI",
            packagePrefix = "no.ruter.tranop.operation.common.bi",
            version = "1",
            schemaFolder = File(localSchemaDir, "operation/common/bi"),
        ),
        SchemaSpec(
            name = "serviceDeviation",
            namePrefix = "DTO",
            packagePrefix = "no.ruter.tranop.journey.deviation.dto",
            version = "1",
            schemaFolder = File(localSchemaDir, "operation/deviation/dto"),
            kafkaSerdeSchemas =
                listOf(
                    "service-deviation.yml",
                ),
            filePatterns = yamlPattern,
        ),
        SchemaSpec(
            name = "biServiceDeviation",
            namePrefix = "BI",
            packagePrefix = "no.ruter.tranop.operation.deviation.bi",
            version = "1",
            schemaFolder = File(localSchemaDir, "operation/deviation/bi"),
        ),
        SchemaSpec(
            name = "serviceMitigation",
            namePrefix = "DTO",
            packagePrefix = "no.ruter.tranop.journey.mitigation.dto",
            version = "1",
            schemaFolder = File(localSchemaDir, "operation/mitigation/dto"),
            kafkaSerdeSchemas =
                listOf(
                    "service-mitigation.yml",
                ),
            filePatterns = yamlPattern,
        ),
        SchemaSpec(
            name = "trafficEvent",
            namePrefix = "DTO",
            packagePrefix = "no.ruter.tranop.traffic.event.dto",
            version = "1",
            schemaFolder = File(localSchemaDir, "traffic/event/dto"),
            kafkaSerdeSchemas =
                listOf(
                    "traffic-event.json",
                ),
        ),
        SchemaSpec(
            name = "journeyEvent",
            namePrefix = "DTO",
            packagePrefix = "no.ruter.tranop.journey.event.dto",
            version = "1",
            schemaFolder = File(localSchemaDir, "journey/event/dto"),
            kafkaSerdeSchemas =
                listOf(
                    "journey-event.json",
                ),
        ),
        SchemaSpec(
            name = "biJourneyEvent",
            namePrefix = "BI",
            packagePrefix = "no.ruter.tranop.journey.event.bi",
            version = "1",
            schemaFolder = File(localSchemaDir, "journey/event/bi"),
        ),
        SchemaSpec(
            generators = listOf(ApiGenerator.SPRING),
            name = "assignment",
            namePrefix = "API",
            version = "2",
            schemaFolder = File(localSchemaDir, "assignment/api/v2"),
            valueTypes = false,
            schemaPath = "schema",
            filePatterns = yamlPattern,
            apiSpecFilePath = "assignment-api-v2.yml",
            packagePrefix = "no.ruter.tranop.assignment.api.v2",
        ),
        SchemaSpec(
            name = "siri",
            namePrefix = "DTO",
            packagePrefix = "no.ruter.tranop.assignment.siri.v2",
            version = "2",
            schemaFolder = File(localSchemaDir, "siri/v20"),
            kafkaSerdeSchemas = listOf("siri20.json"),
        ),
        SchemaSpec(
            name = "itxpt",
            version = "1",
            namePrefix = "DTO",
            packagePrefix = "no.ruter.tranop.assignment.adt.v1",
            git =
                GitSpec(
                    url = "https://github.com/RuterNo/adt-doc.git",
                    tag = libs.versions.adt.doc.v1,
                ),
            schemaPath = "asyncapi/json-schemas",
            filePatterns =
                listOf(
                    "notification.json",
                    "signon.json",
                ),
            kafkaSerdeSchemas =
                listOf(
                    "signon.json",
                ),
        ),
        SchemaSpec(
            name = "adt",
            version = "2",
            namePrefix = "ADT",
            packagePrefix = "no.ruter.tranop.assignment.adt.v2",
            git =
                GitSpec(
                    url = "https://github.com/RuterNo/adt-doc.git",
                    tag = libs.versions.adt.doc.v2,
                ),
            schemaPath = "asyncapi/json-schemas",
            filePatterns =
                listOf(
                    "assignment-attempt.json",
                    "assignment-attempt-rejection.json",
                    "current-block.json",
                ),
        ),
        SchemaSpec(
            name = "adt",
            version = "3",
            namePrefix = "ADT",
            packagePrefix = "no.ruter.tranop.assignment.adt.v3",
            git =
                GitSpec(
                    url = "https://github.com/RuterNo/adt-doc.git",
                    tag = libs.versions.adt.doc.v3,
                ),
            schemaPath = "asyncapi/json-schemas",
            filePatterns =
                listOf(
                    "operational/**/*.json",
                ),
        ),
        SchemaSpec(
            name = "adtDisplays",
            version = "2",
            namePrefix = "ADT",
            packagePrefix = "no.ruter.tranop.displays.adt.v2",
            git =
                GitSpec(
                    url = "https://github.com/RuterNo/adt-doc.git",
                    tag = libs.versions.adt.doc.v2,
                ),
            schemaPath = "asyncapi/json-schemas",
            filePatterns =
                listOf(
                    "available-destination-displays.json",
                    "destination-display-override.json",
                ),
            kafkaSerdeSchemas =
                listOf(
                    "available-destination-displays.json",
                    "destination-display-override.json",
                ),
        ),
        SchemaSpec(
            name = "adtDisplays",
            version = "3",
            namePrefix = "ADT",
            packagePrefix = "no.ruter.tranop.displays.adt.v3",
            git =
                GitSpec(
                    url = "https://github.com/RuterNo/adt-doc.git",
                    tag = libs.versions.adt.doc.v3,
                ),
            schemaPath = "asyncapi/json-schemas",
            kafkaSerdeSchemas =
                listOf(
                    "di/available-destination-displays/available-destination-displays.json",
                    "di/override_attempt/destination_display/destination-display-override.json",
                ),
            filePatterns =
                listOf(
                    "di/**/*.json",
                    "di/*.json",
                ),
        ),
        SchemaSpec(
            name = "adtDisplays",
            version = "4",
            namePrefix = "ADT",
            packagePrefix = "no.ruter.tranop.displays.adt.v4",
            git =
                GitSpec(
                    url = "https://github.com/RuterNo/adt-doc.git",
                    tag = libs.versions.adt.doc.v4,
                ),
            schemaPath = "asyncapi/json-schemas",
            kafkaSerdeSchemas =
                listOf(
                    "di/available-destination-displays/available-destination-displays.json",
                    "di/override_attempt/destination_display/destination-display-override.json",
                ),
            filePatterns =
                listOf(
                    "di/**/*.json",
                    "di/*.json",
                ),
        ),
        SchemaSpec(
            generators = listOf(ApiGenerator.SPRING),
            name = "operational",
            version = "4",
            namePrefix = "ADT",
            packagePrefix = "no.ruter.tranop.assignment.adt.v4",
            valueTypes = true,
            git =
                GitSpec(
                    url = "https://gitlab.com/ruter-as/tranop/assignment/common/assignment-adt-v4-api-model.git",
                    tag = libs.versions.assignment.adt.v4.api.model,
                    credentials = Credentials(gitLabUsername, gitLabPassword),
                ),
            schemaPath = "src/main/resources/api/v4/schema",
            filePatterns = yamlPattern,
            apiSpecFilePath = "api-adt-v4-operational.yml",
        ),
        avroSpec,
    )

tasks.register("cloneRepo") {
    doLast {
        specs
            .filter { it.git != null }
            .forEach { repo ->
                val dest = repo.schemaFolder
                val git = repo.git!!
                if (!dest.exists()) {
                    dest.mkdirs()
                    val props =
                        mutableMapOf<String, Any>(
                            "dir" to dest,
                            "uri" to git.url,
                        )
                    git.credentials?.let {
                        props.put("credentials", it)
                    }
                    val grGit = Grgit.clone(props)
                    grGit.checkout(mapOf("branch" to git.tag.get()))
                }
            }
    }
}

val copySchemaFiles =
    tasks.register("copySchemaFiles") {
        dependsOn("cloneRepo")
        doLast {
            specs.forEach { spec ->
                val dest = spec.workDir
                if (!dest.exists()) {
                    dest.mkdirs()
                    println("Making $dest")
                }

                copy {
                    from(File(spec.schemaFolder, spec.schemaPath)) {
                        include(spec.filePatterns)
                    }
                    into(dest)
                    filter { line ->
                        var s = line
                        spec.replacements.forEach { (key, value) -> s = s.replace(key, value) }
                        s
                    }
                }
            }
        }
    }

tasks.named("generateAvroJava") {
    dependsOn(copySchemaFiles)
}

val testCodegen =
    tasks.named("generateTestAvroJava") {
        dependsOn(copySchemaFiles)
    }

val deleteImportedSchemas =
    tasks.register("deleteImportedSchemasApi") {
        doLast {
            println("Deleting ${avroSpec.workDir}")
            delete(avroSpec.workDir)

            println("Deleting $importedSchemasDir")
            delete(importedSchemasDir)
        }
    }

tasks.clean {
    dependsOn(deleteImportedSchemas)
}

tasks.getByName("tranopCodegen") {
    dependsOn(copySchemaFiles)
}

tranopCodegen {
    jsonSchemas {
        specs
            .forEach { spec ->
                spec.generators
                    .forEach { genSpec ->
                        val name = "${spec.name}${spec.namePrefix}V${spec.version}"
                        println("TranopCodeGen $name")
                        val yamlFiles =
                            spec.filePatterns
                                .any { it.lowercase().contains(".yml") || it.lowercase().contains(".yaml") }
                        create(name) {
                            modelName = name
                            io {
                                target = codegenTargetDir
                                source = "${spec.workDir}"
                                if (yamlFiles) {
                                    formats.set(listOf(JsonSchemaFormat.YAML))
                                }
                            }
                            classes {
                                namePrefix = spec.namePrefix
                                modelTypes = genSpec.isTranop()
                                valueTypes = true
                                basePackage = spec.packagePrefix
                            }
                            kafkaSerdeSchemas.set(
                                spec.kafkaSerdeSchemas,
                            )
                        }
                    }
            }
    }
}

specs.forEach { spec ->
    spec.generators
        .filter { it.isOpenApi() }
        .forEach { genSpec ->
            val jobName = "generateJarPackage_${spec.name}${spec.version}_$genSpec"
            tasks.register<org.openapitools.generator.gradle.plugin.tasks.GenerateTask>(
                name = jobName,
            ) {
                dependsOn("printDirectoryTree")
                dependsOn("copySchemaFiles")
                val targetDir = "$codegenTargetDir/api/${spec.name}${spec.namePrefix}V${spec.version}"
                inputSpec.set("${spec.workDir}/${spec.apiSpecFilePath}")
                outputDir.set(targetDir)
                modelNamePrefix = "API"
                apiPackage.set("${spec.packagePrefix}.api")
                modelPackage.set("${spec.packagePrefix}.model")

                generatorName.set(genSpec.generatorName)

                configOptions.put("serializationLibrary", "jackson")
                configOptions.put("interfaceOnly", "true")
                configOptions.put("useSpringBoot3", "true")
                configOptions.put("gradleBuildFile", "false")
                configOptions.put("exceptionHandler", "false")
                configOptions.put("useBeanValidation", "false")
                configOptions.put("swaggerAnnotations", "false")
                configOptions.put("skipDefaultInterface", "true")
                configOptions.put("documentationProvider", "source")
                configOptions.put("requestMappingMode", "api_interface")
                sourceSets {
                    kotlin {
                        main.get().kotlin.srcDir("$targetDir/src/main/kotlin")
                    }
                }
            }
        }
}

tasks.register("printDirectoryTree") {
    dependsOn("copySchemaFiles")
    doLast {
        val directoryToPrint = File(file(buildDir), "resources/main")
        println("Copied spec files ${directoryToPrint.path}:")
        printDirectoryTree(directoryToPrint)
    }
}

val codegen =
    tasks.register("codegen") {
        dependsOn("cloneRepo")
        dependsOn("copySchemaFiles")
        dependsOn("printDirectoryTree")
        dependsOn("tranopCodegen")
        dependsOn("generateAvroJava")
        specs.forEach { spec ->
            spec.generators
                .filter { it.isOpenApi() }
                .forEach { genSpec ->
                    val jobName = "generateJarPackage_${spec.name}${spec.version}_$genSpec"
                    dependsOn(jobName)
                }
        }
    }

tasks.withType<Test> {
    useJUnitPlatform()
    testlogger {
        showPassed = true
        showExceptions = true
        showFullStackTraces = true
        showFailedStandardStreams = true
    }
}

tasks.withType<KotlinCompile> {
    dependsOn(codegen)
}
tasks.withType<JavaCompile> {
    dependsOn(codegen)
}

tasks.named<Jar>("sourcesJar") {
    dependsOn(codegen)
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

fun RepositoryHandler.gitlabGroup(
    groupId: String,
    groupName: String,
) {
    maven {
        name = "gitlabGroup${groupName}Maven"
        setUrl("https://gitlab.com/api/v4/groups/$groupId/-/packages/maven")
        credentials(HttpHeaderCredentials::class.java) {
            if (!gitLabCI) {
                name = "Private-Token"
                value = extra.properties["gitLabPrivateToken"] as String? ?: error(
                    "Missing required Gradle property (~/.gradle/gradle.properties): gitLabPrivateToken",
                )
            } else {
                name = "Job-Token"
                value = jobToken
            }
            println("Using $name for Maven dependencies")
        }
        content {
            includeGroupByRegex("no\\..*")
        }
        authentication {
            create<HttpHeaderAuthentication>("header")
        }
    }
}

// Start - Config for com.github.ben-manes.versions
// Without this config the result of gradle dependencyUpdates will take the latest Milesone / not Stable versions
fun isNonStable(version: String): Boolean {
    val stableKeyword = listOf("RELEASE", "FINAL", "GA").any { version.uppercase().contains(it) }
    val regex = "^[0-9,.v-]+(-r)?$".toRegex()
    val isStable = stableKeyword || regex.matches(version)
    return isStable.not()
}

tasks.withType<DependencyUpdatesTask> {
    rejectVersionIf {
        isNonStable(candidate.version) && !isNonStable(currentVersion)
    }
}
// End - Config for com.github.ben-manes.versions

fun printDirectoryTree(
    file: File,
    indent: String = "",
) {
    if (!file.exists()) {
        println("Directory ${file.path} does not exist.")
        return
    }

    if (file.isDirectory) {
        println("$indent- ${file.name}/")
        file.listFiles()?.forEach { child ->
            printDirectoryTree(child, "$indent  ")
        }
    } else {
        println("$indent- ${file.path}")
    }
}
