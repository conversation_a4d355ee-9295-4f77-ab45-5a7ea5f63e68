{"type": "object", "title": "Dated<PERSON><PERSON>ney", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["header"], "additionalProperties": true, "properties": {"ref": {"type": "string"}, "header": {"type": "object", "existingJavaType": "no.ruter.tranop.common.dto.model.DTOMessageHeader"}, "events": {"type": "array", "items": {"$ref": "./common/event.json"}}, "lifeCycleInfo": {"type": "object", "existingJavaType": "no.ruter.tranop.common.dto.model.DTOLifeCycleInfo"}, "cancelled": {"type": "boolean", "description": "Set if journey is cancelled, indicating it will not be operated by any vehicle."}, "deleted": {"type": "boolean", "description": "Set if journey is deleted, indicating it is no longer part of any active plan."}, "omitted": {"type": "boolean", "description": "Set if journey is omitted by operator: the operator has no intention of servicing this journey."}, "public": {"type": "boolean", "description": "If the trip is for the public or for specific travellers. (E.g. a school bus)"}, "type": {"type": "string", "examples": ["DeadRun        # name=DEAD_RUN; desc=A dead run journey;", "ServiceJourney # name=SERVICE_JOURNEY; desc=A regular service journey;"], "existingJavaType": "no.ruter.tranop.dated.journey.dto.model.common.DTOJourneyType"}, "extra": {"type": "boolean", "description": "True if this journey was created outside the normal planning process (alias: ad-hoc, replacement, extra, new). Otherwise, false"}, "name": {"type": "string"}, "vehicleTask": {"type": "string"}, "line": {"$ref": "./common/line.json"}, "journeyReferences": {"$ref": "dated-journey-references.json"}, "operators": {"type": "array", "items": {"$ref": "./common/operator.json"}}, "operatorContracts": {"type": "array", "items": {"$ref": "./common/operator-contract.json"}}, "lineage": {"type": "array", "items": {"$ref": "./common/lineage.json"}}, "vehicles": {"type": "array", "items": {"$ref": "./common/vehicle.json"}}, "operatingDate": {"type": "string"}, "plan": {"$ref": "dated-journey-plan.json"}, "direction": {"existingJavaType": "no.ruter.tranop.dated.journey.dto.model.common.DTODatedJourneyDirectionCode"}, "order": {"type": "integer"}, "journeyState": {"description": "Dated Journey state information.", "$ref": "./common/journey-state.json", "default": null}, "replaces": {"$ref": "dated-journey-replaces.json", "description": "Information about journeys this journey replaces if any.", "default": null}, "replacedBy": {"$ref": "dated-journey-replaced-by.json", "description": "Information about journeys this journey is replaced by if any. For internal lookup", "default": null}}}