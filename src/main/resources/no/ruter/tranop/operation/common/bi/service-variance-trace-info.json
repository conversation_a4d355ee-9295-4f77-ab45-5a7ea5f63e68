{"type": "object", "title": "ServiceVarianceTraceInfo", "$schema": "http://json-schema.org/draft-07/schema#", "description": "Trace information containing ownership and operational context identifiers.", "properties": {"ownerId": {"type": "string", "description": "Identifier for the data owner."}, "operatorId": {"type": "string", "description": "Identifier for the operator."}, "authorityId": {"type": "string", "description": "Identifier for the authority."}}}