type: object
title: ServiceDeviation
properties:
  ref:
    type: string
  header:
    type: object
    existingJavaType: "no.ruter.tranop.common.dto.model.DTOMessageHeader"
  trace:
    existingJavaType: "no.ruter.tranop.operation.common.dto.model.DTOServiceVarianceTraceInfo"
  spec:
    $ref: "./service-deviation-spec.yml"
  lifecycle:
    type: "object"
    existingJavaType: "no.ruter.tranop.common.dto.model.DTOLifeCycleInfo"
