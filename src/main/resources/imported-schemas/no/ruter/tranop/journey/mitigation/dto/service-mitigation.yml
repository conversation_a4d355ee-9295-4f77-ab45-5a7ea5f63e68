type: object
title: ServiceMitigation
properties:
  ref:
    type: string
  header:
    type: object
    existingJavaType: "no.ruter.tranop.common.dto.model.DTOMessageHeader"
  spec:
    $ref: "./service-mitigation-spec.yml"
  trace:
    existingJavaType: "no.ruter.tranop.operation.common.dto.model.DTOServiceVarianceTraceInfo"
  draft:
    type: boolean
  action:
    type: string
  lifecycle:
    type: "object"
    existingJavaType: "no.ruter.tranop.common.dto.model.DTOLifeCycleInfo"
