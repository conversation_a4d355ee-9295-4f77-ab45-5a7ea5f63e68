type: string
title: StatusResponseReasonCode
description: >
    Machine readable specifications of the code supplied. The list of examples
    might not be complete.
existingJavaType: no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseReasonCode
example:
  - "OK                             # OK / No reason"
  - "INFO                           # Informational only. Description field(s) may contain additional details."
  - "WARNING                        # Warning only. Description fields may contain additional details."

  - "TIMEOUT                        # A timeout (error) occurred."
  - "BAD_REQUEST                    # The provided request contained one or more error(s)."
  - "INTERNAL_ERROR                 # An internal error occurred."

  - "CALL_SPEC_ERROR                # Missing or invalid call specification(s)."
  - "LINE_SPEC_ERROR                # Missing or invalid line specification(s)."
  - "JOURNEY_SPEC_ERROR             # Missing or invalid journey specification(s)"
  - "SERVICE_WINDOW_ERROR           # Missing or invalid service window(s) in specification(s)."
  - "STOP_POINT_SPEC_ERROR          # Missing or invalid stop point specification(s)"

  - "CALL_NOT_FOUND                 # No call(s) matching provided specification(s)."
  - "LINE_NOT_FOUND                 # No line(s) matching provided specification(s)."
  - "JOURNEY_NOT_FOUND              # No journey(s) matching provided specification(s)."
  - "STOP_POINT_NOT_FOUND           # No stop point(s) matching provided specification(s)."

  - "VEHICLE_ID_ERROR               # Missing or invalid vehicle id in request."
  - "OPERATOR_ID_ERROR              # Missing or invalid operator id in request."

  - "DEVIATION_CODE_ERROR           # Missing or invalid service deviation code."
  - "DEVIATION_REASON_CODE_ERROR    # Missing or invalid service deviation reason code."

  - "MITIGATION_CODE_ERROR          # Missing or invalid service mitigation code."

  - "ASSIGNMENT_ATTEMPT_EXPIRED     # Assignment attempt has expired (timeout error)."
  - "ASSIGNMENT_SIGN_OFF_CODE_ERROR # Missing or invalid assignment attempt sign-off code."
