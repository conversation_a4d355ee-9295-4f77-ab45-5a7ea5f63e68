{"type": "record", "name": "LifeCycleInfo", "namespace": "no.ruter.avro.operational.common", "doc": "Lifecycle information with timestamps and revision tracking", "fields": [{"name": "created", "doc": "When current revision was created (ISO 8601 format)", "type": "string"}, {"name": "modified", "doc": "When last update happened (ISO 8601 format)", "type": "string"}, {"name": "revision", "doc": "Monotonically increasing number which increases with each change in given journey", "type": "int"}]}