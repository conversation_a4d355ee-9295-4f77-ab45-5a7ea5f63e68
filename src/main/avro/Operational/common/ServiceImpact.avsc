{"type": "record", "name": "ServiceImpact", "namespace": "no.ruter.avro.operational.common", "doc": "Service impact", "fields": [{"name": "lines", "doc": "Lines affected by the service deviation/mitigation", "type": ["null", {"type": "array", "items": "no.ruter.avro.operational.common.spec.JourneyLineSpecWindow"}], "default": null}, {"name": "journeys", "doc": "Journeys affected by the service deviation/mitigation", "type": ["null", {"type": "array", "items": "no.ruter.avro.operational.common.spec.JourneySpecWindowOption"}], "default": null}, {"name": "stopPoints", "doc": "Stop points affected by the service deviation/mitigation", "type": ["null", {"type": "array", "items": "no.ruter.avro.operational.common.spec.StopPointSpecWindow"}], "default": null}]}