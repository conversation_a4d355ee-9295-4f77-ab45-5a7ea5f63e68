{"type": "record", "name": "StopPointSpecWindow", "namespace": "no.ruter.avro.operational.common.spec", "doc": "Stop point specification with service window", "fields": [{"name": "spec", "doc": "Stop point specification", "type": "no.ruter.avro.operational.common.spec.StopPointSpec"}, {"name": "serviceWindow", "doc": "Service window for the stop point", "type": ["null", "no.ruter.avro.operational.common.DateTimeRange"], "default": null}]}