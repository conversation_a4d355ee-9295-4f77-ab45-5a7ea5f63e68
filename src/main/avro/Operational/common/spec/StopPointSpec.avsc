{"type": "record", "name": "StopPointSpec", "namespace": "no.ruter.avro.operational.common.spec", "doc": "Stop point specification", "fields": [{"name": "nsrQuayRef", "doc": "Unique identifier of (NSR) quay, if any. May also be an internal quay id not officially registered in NSR, such as a garage or vehicle depot", "type": ["null", "string"], "default": null}, {"name": "entityDatedJourneyStopPointKeyV2Ref", "doc": "Unique id of stop point. May be used as an alternative to quayId if stop point has no quay id or quay id is not known", "type": ["null", "string"], "default": null}]}