{"type": "record", "name": "JourneySpec", "namespace": "no.ruter.avro.operational.common.spec", "doc": "Journey specification", "fields": [{"name": "lineId", "doc": "Line identifier. Can be a reference to an existing line entity.", "type": "string"}, {"name": "journeyId", "doc": "Identifier referencing journey, such as serviceJourneyId, vehicleJourneyId or datedServiceJourneyId", "type": "string"}, {"name": "firstDepartureDateTime", "doc": "Planned Departure Time from the initial call", "type": "string"}]}