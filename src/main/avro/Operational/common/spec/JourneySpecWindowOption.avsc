{"type": "record", "name": "JourneySpecWindowOption", "namespace": "no.ruter.avro.operational.common.spec", "doc": "Journey specification window option with optional calls", "fields": [{"name": "calls", "doc": "Optional list of specific calls within the journey that are affected", "type": ["null", {"type": "array", "items": "no.ruter.avro.operational.common.spec.JourneyCallSpec"}], "default": null}, {"name": "journey", "doc": "Journey specification with service window", "type": "no.ruter.avro.operational.common.spec.JourneySpecWindow"}]}