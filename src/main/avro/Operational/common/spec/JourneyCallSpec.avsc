{"type": "record", "name": "JourneyCallSpec", "namespace": "no.ruter.avro.operational.common.spec", "doc": "Journey call specification", "fields": [{"name": "stopPoint", "doc": "Stop point specification", "type": "no.ruter.avro.operational.common.spec.StopPointSpec"}, {"name": "departureDateTime", "doc": "Departure date and time for this call (ISO 8601)", "type": ["null", "string"], "default": null}, {"name": "arrivalDateTime", "doc": "Arrival date and time for this call (ISO 8601)", "type": ["null", "string"], "default": null}]}