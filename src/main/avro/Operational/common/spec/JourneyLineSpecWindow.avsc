{"type": "record", "name": "JourneyLineSpecWindow", "namespace": "no.ruter.avro.operational.common.spec", "doc": "Journey line specification with service window", "fields": [{"name": "spec", "doc": "Journey line specification", "type": "no.ruter.avro.operational.common.spec.JourneyLineSpec"}, {"name": "serviceWindow", "doc": "Service window for the line", "type": ["null", "no.ruter.avro.operational.common.DateTimeRange"], "default": null}]}