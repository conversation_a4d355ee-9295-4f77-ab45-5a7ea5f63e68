{"type": "record", "name": "JourneySpecWindow", "namespace": "no.ruter.avro.operational.common.spec", "doc": "Journey specification with service window", "fields": [{"name": "spec", "doc": "Journey specification", "type": "no.ruter.avro.operational.common.spec.JourneySpec"}, {"name": "serviceWindow", "doc": "Service window for the journey", "type": ["null", "no.ruter.avro.operational.common.DateTimeRange"], "default": null}]}