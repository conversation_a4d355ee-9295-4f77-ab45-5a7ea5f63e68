{"type": "record", "name": "DateTimeRange", "namespace": "no.ruter.avro.operational.common", "doc": "A date-time range with a start date-time and an end date-time", "fields": [{"name": "start", "doc": "Start of date-time range in ISO-8601 format (format: YYYY-MM-DDTHH:mm:ssZ)", "type": "string"}, {"name": "end", "doc": "End of date-time range in ISO-8601 format (format: YYYY-MM-DDTHH:mm:ssZ)", "type": "string"}]}