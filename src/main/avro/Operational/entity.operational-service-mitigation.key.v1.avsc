{"type": "record", "name": "OperationalServiceMitigationKeyV1", "namespace": "no.ruter.avro.entity.operational", "doc": "Service Mitigation Entity Key V1 based on correct ADT v4 specification", "fields": [{"name": "entityHeader", "type": "no.ruter.avro.common.EntityHeaderV2", "doc": "The data of the entity header"}, {"name": "entityData", "doc": "The data of the entity", "type": "no.ruter.avro.entity.operational.mitigation.ServiceMitigationV1"}]}