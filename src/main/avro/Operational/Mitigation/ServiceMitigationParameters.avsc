{"type": "record", "name": "ServiceMitigationParameters", "namespace": "no.ruter.avro.operational.mitigation", "doc": "Parameters of mitigation", "fields": [{"name": "vehicleId", "type": ["null", "string"], "doc": "Optional identifier of vehicle to which the service mitigation applies"}, {"name": "transportMode", "type": ["null", "string"], "doc": "Transport mode"}, {"name": "operatorId", "type": ["null", "string"], "doc": "Optional identifier of participating operator."}, {"name": "updateAssignedVehicles", "type": "boolean", "doc": "Update vehicle state for assigned vehicles impacted by this mitigation."}]}