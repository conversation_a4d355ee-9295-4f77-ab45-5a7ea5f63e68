{"type": "record", "name": "ServiceMitigationV1", "namespace": "no.ruter.avro.entity.operational.mitigation", "doc": "Service Mitigation V1", "fields": [{"name": "ref", "type": "string", "doc": "Unique reference identifier for the service mitigation"}, {"name": "spec", "type": "no.ruter.avro.operational.mitigation.ServiceMitigationSpecV1", "doc": "Service mitigation specification"}, {"name": "lifecycle", "type": "no.ruter.avro.operational.common.LifeCycleInfo", "doc": "Lifecycle information with timestamps and mitigation id"}]}