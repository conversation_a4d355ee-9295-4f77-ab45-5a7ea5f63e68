{"type": "record", "name": "ServiceMitigationSpecV1", "namespace": "no.ruter.avro.operational.mitigation", "doc": "Service Mitigation Specification V1", "fields": [{"name": "code", "type": "string", "doc": "Service mitigation code, indicating type of service mitigation. Examples: CANCELLATION (Indicating that a journey will be canceled), REPLACEMENT_SERVICE (Indicating that a replacement service will be provided), STANDBY_VEHICLE_PLANNED (Indicating that a standby vehicle will be used)."}, {"name": "impact", "type": "no.ruter.avro.operational.common.ServiceImpact", "doc": "Impact of the service mitigation using common impact structure"}, {"name": "duration", "type": "no.ruter.avro.operational.common.DateTimeRange", "doc": "Duration and time range of the service mitigation"}, {"name": "mitigates", "type": {"type": "array", "items": "string"}, "doc": "Reference to what this mitigation mitigates"}, {"name": "metadata", "type": {"type": "array", "items": "no.ruter.avro.operational.common.MetadataEntry"}, "doc": "A mitigation metadata entry. Examples: PTO_CASE_REF (A PTO case reference), PTA_CASE_REF (A PTA case reference), SERVICE_DEVIATION_REF (A service deviation reference), SERVICE_MITIGATION_REF (A service mitigation reference)."}, {"name": "parameters", "type": "no.ruter.avro.operational.mitigation.ServiceMitigationParameters", "doc": "Parameters of mitigation"}]}