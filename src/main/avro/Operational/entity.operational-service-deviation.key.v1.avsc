{"type": "record", "name": "OperationalServiceDeviationKeyV1", "namespace": "no.ruter.avro.entity.operational", "doc": "Service Deviation Entity Key V1 based on correct ADT v4 specification", "fields": [{"name": "entityHeader", "type": "no.ruter.avro.common.EntityHeaderV2", "doc": "The data of the entity header"}, {"name": "entityData", "doc": "The data of the entity", "type": "no.ruter.avro.entity.operational.deviation.ServiceDeviationV1"}]}