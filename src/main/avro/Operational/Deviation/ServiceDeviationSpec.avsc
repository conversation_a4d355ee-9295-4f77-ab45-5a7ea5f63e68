{"type": "record", "name": "ServiceDeviationSpec", "namespace": "no.ruter.avro.operational.deviation", "doc": "Service Deviation Specification", "fields": [{"name": "code", "type": "string", "doc": "Service deviation code, indicating type of service deviation. Examples: DELAY (Service will be delayed), NO_SERVICE (Journey(s), line(s) or call(s) will not be serviced by operator), NO_SIGN_ON (Planned journey(s), line(s) or call(s) will be serviced, but operator will not sign on)."}, {"name": "reason", "type": "no.ruter.avro.operational.deviation.ServiceDeviationReason", "doc": "Service deviation reason, indicating reason for service deviation"}, {"name": "impact", "type": "no.ruter.avro.operational.common.ServiceImpact", "doc": "Impact of the service deviation using common impact structure"}, {"name": "duration", "type": "no.ruter.avro.operational.common.DateTimeRange", "doc": "Duration and time range of the service deviation"}, {"name": "metadata", "type": {"type": "array", "items": "no.ruter.avro.operational.common.MetadataEntry"}, "doc": "A deviation metadata entry. Examples: PTO_CASE_REF (A PTO case reference), PTA_CASE_REF (A PTA case reference), SERVICE_DEVIATION_REF (A service deviation reference), SERVICE_MITIGATION_REF (A service mitigation reference)."}, {"name": "parameters", "type": "no.ruter.avro.operational.deviation.ServiceDeviationParameters", "doc": "Parameters of deviation"}]}