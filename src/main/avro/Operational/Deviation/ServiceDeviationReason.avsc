{"type": "record", "name": "ServiceDeviationReason", "namespace": "no.ruter.avro.operational.deviation", "doc": "Service deviation reason, indicating reason for service deviation", "fields": [{"name": "code", "type": "string", "doc": "Service deviation reason code. Allowed values depends on deviation code, operator and active configuration. A list of available codes can be obtained via API look-up"}, {"name": "comment", "type": ["null", "string"], "doc": "Optional, human-readable, reason comment, if applicable"}]}