{"type": "record", "name": "ServiceDeviationV1", "namespace": "no.ruter.avro.entity.operational.deviation", "doc": "Service Deviation V1", "fields": [{"name": "ref", "type": "string", "doc": "Unique reference identifier for the service deviation"}, {"name": "spec", "type": "no.ruter.avro.operational.deviation.ServiceDeviationSpec", "doc": "Service deviation specification"}, {"name": "lifecycle", "type": "no.ruter.avro.operational.common.LifeCycleInfo", "doc": "Lifecycle information with timestamps and deviation id"}]}