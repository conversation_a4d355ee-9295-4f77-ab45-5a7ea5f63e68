{"type": "record", "name": "ServiceDeviationParameters", "namespace": "no.ruter.avro.operational.deviation", "doc": "Parameters of deviation", "fields": [{"name": "vehicleId", "type": ["null", "string"], "doc": "Optional identifier of vehicle to which the service deviation applies"}, {"name": "delayMinutes", "type": ["null", "int"], "doc": "Number of minutes of expected delay, relative to planned arrival / departure time"}, {"name": "operatorExempt", "type": ["null", "boolean"], "doc": "Flag indicating that operator is exempt from consequences of reported service deviation"}, {"name": "updateAssignedVehicles", "type": "boolean", "doc": "Update vehicle state for assigned vehicles impacted by this deviation."}]}